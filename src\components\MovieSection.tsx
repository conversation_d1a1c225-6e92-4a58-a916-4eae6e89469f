import { useState, useEffect } from 'react'
import MovieCard from './MovieCard'
import type { Movie } from '../types/movie'
import movieApi from '../services/movieApi'

interface MovieSectionProps {
  title: string
  type?: 'trending' | 'popular' | 'new-releases' | 'action' | 'comedy' | 'tv-shows'
}

const MovieSection = ({ title, type = 'trending' }: MovieSectionProps) => {
  const [movies, setMovies] = useState<Movie[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [scrollPosition, setScrollPosition] = useState(0)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(true)

  // Fetch movies based on type
  useEffect(() => {
    const fetchMovies = async () => {
      try {
        setLoading(true)
        setError(null)

        let response
        switch (type) {
          case 'trending':
            response = await movieApi.getTrendingMovies(20)
            break
          case 'popular':
            response = await movieApi.getPopularMovies(20)
            break
          case 'new-releases':
            response = await movieApi.getNewMovies(1)
            break
          case 'action':
            response = await movieApi.getActionMovies(20)
            break
          case 'comedy':
            response = await movieApi.getComedyMovies(20)
            break
          case 'tv-shows':
            response = await movieApi.getTVShows(20)
            break
          default:
            response = await movieApi.getTrendingMovies(20)
        }

        if (response.status && response.data.items) {
          setMovies(response.data.items)
        } else {
          setError('Không thể tải danh sách phim')
        }
      } catch (err) {
        setError('Lỗi khi tải dữ liệu phim')
        console.error('Error fetching movies:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchMovies()
  }, [type])

  const handleScroll = (direction: 'left' | 'right') => {
    const container = document.getElementById(`movie-container-${title.replace(/\s+/g, '-')}`)
    if (!container) return

    const scrollAmount = 320 // Width of one movie card + gap
    const newPosition = direction === 'left'
      ? Math.max(0, scrollPosition - scrollAmount)
      : scrollPosition + scrollAmount

    container.scrollTo({
      left: newPosition,
      behavior: 'smooth'
    })

    setScrollPosition(newPosition)
    setCanScrollLeft(newPosition > 0)
    setCanScrollRight(newPosition < container.scrollWidth - container.clientWidth)
  }

  return (
    <section className="space-y-4">
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl md:text-3xl font-bold text-white">{title}</h2>
        <div className="flex space-x-2">
          <button
            onClick={() => handleScroll('left')}
            disabled={!canScrollLeft}
            className={`p-2 rounded-full transition-colors ${
              canScrollLeft
                ? 'bg-gray-800 hover:bg-gray-700 text-white'
                : 'bg-gray-800/50 text-gray-500 cursor-not-allowed'
            }`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            onClick={() => handleScroll('right')}
            disabled={!canScrollRight}
            className={`p-2 rounded-full transition-colors ${
              canScrollRight
                ? 'bg-gray-800 hover:bg-gray-700 text-white'
                : 'bg-gray-800/50 text-gray-500 cursor-not-allowed'
            }`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      {/* Movies Container */}
      <div className="relative">
        {loading && (
          <div className="flex space-x-4">
            {[...Array(7)].map((_, index) => (
              <div key={index} className="flex-shrink-0 w-72">
                <div className="bg-gray-800 rounded-lg h-96 animate-pulse"></div>
                <div className="mt-3 space-y-2">
                  <div className="bg-gray-800 h-4 rounded animate-pulse"></div>
                  <div className="bg-gray-800 h-3 rounded w-2/3 animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        )}

        {error && (
          <div className="text-center py-8">
            <p className="text-red-400 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Thử lại
            </button>
          </div>
        )}

        {!loading && !error && movies.length > 0 && (
          <div
            id={`movie-container-${title.replace(/\s+/g, '-')}`}
            className="flex space-x-4 overflow-x-auto scrollbar-hide scroll-smooth"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {movies.map((movie) => (
              <MovieCard key={movie._id} movie={movie} />
            ))}
          </div>
        )}

        {!loading && !error && movies.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-400">Không có phim nào để hiển thị</p>
          </div>
        )}
      </div>
    </section>
  )
}

export default MovieSection
