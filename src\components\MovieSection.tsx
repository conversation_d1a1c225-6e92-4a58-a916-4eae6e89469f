import { useState } from 'react'
import MovieCard from './MovieCard'

interface MovieSectionProps {
  title: string
}

// Sample movie data
const sampleMovies = [
  {
    id: 1,
    title: "Spider-Man: No Way Home",
    image: "https://images.unsplash.com/photo-1635805737707-575885ab0820?w=300&h=450&fit=crop",
    rating: 8.4,
    year: 2021,
    duration: "2h 28min"
  },
  {
    id: 2,
    title: "Dune",
    image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=450&fit=crop",
    rating: 8.0,
    year: 2021,
    duration: "2h 35min"
  },
  {
    id: 3,
    title: "The Batman",
    image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=450&fit=crop",
    rating: 7.8,
    year: 2022,
    duration: "2h 56min"
  },
  {
    id: 4,
    title: "Top Gun: Maverick",
    image: "https://images.unsplash.com/photo-1583608205776-bfd35f0d9f83?w=300&h=450&fit=crop",
    rating: 8.3,
    year: 2022,
    duration: "2h 11min"
  },
  {
    id: 5,
    title: "Avatar: The Way of Water",
    image: "https://images.unsplash.com/photo-1544198365-f5d60b6d8190?w=300&h=450&fit=crop",
    rating: 7.6,
    year: 2022,
    duration: "3h 12min"
  },
  {
    id: 6,
    title: "Black Panther: Wakanda Forever",
    image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=450&fit=crop",
    rating: 6.7,
    year: 2022,
    duration: "2h 41min"
  },
  {
    id: 7,
    title: "Doctor Strange in the Multiverse of Madness",
    image: "https://images.unsplash.com/photo-1635805737707-575885ab0820?w=300&h=450&fit=crop",
    rating: 6.9,
    year: 2022,
    duration: "2h 6min"
  }
]

const MovieSection = ({ title }: MovieSectionProps) => {
  const [scrollPosition, setScrollPosition] = useState(0)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(true)

  const handleScroll = (direction: 'left' | 'right') => {
    const container = document.getElementById(`movie-container-${title.replace(/\s+/g, '-')}`)
    if (!container) return

    const scrollAmount = 320 // Width of one movie card + gap
    const newPosition = direction === 'left' 
      ? Math.max(0, scrollPosition - scrollAmount)
      : scrollPosition + scrollAmount

    container.scrollTo({
      left: newPosition,
      behavior: 'smooth'
    })

    setScrollPosition(newPosition)
    setCanScrollLeft(newPosition > 0)
    setCanScrollRight(newPosition < container.scrollWidth - container.clientWidth)
  }

  return (
    <section className="space-y-4">
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl md:text-3xl font-bold text-white">{title}</h2>
        <div className="flex space-x-2">
          <button 
            onClick={() => handleScroll('left')}
            disabled={!canScrollLeft}
            className={`p-2 rounded-full transition-colors ${
              canScrollLeft 
                ? 'bg-gray-800 hover:bg-gray-700 text-white' 
                : 'bg-gray-800/50 text-gray-500 cursor-not-allowed'
            }`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button 
            onClick={() => handleScroll('right')}
            disabled={!canScrollRight}
            className={`p-2 rounded-full transition-colors ${
              canScrollRight 
                ? 'bg-gray-800 hover:bg-gray-700 text-white' 
                : 'bg-gray-800/50 text-gray-500 cursor-not-allowed'
            }`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      {/* Movies Container */}
      <div className="relative">
        <div 
          id={`movie-container-${title.replace(/\s+/g, '-')}`}
          className="flex space-x-4 overflow-x-auto scrollbar-hide scroll-smooth"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {sampleMovies.map((movie) => (
            <MovieCard key={movie.id} movie={movie} />
          ))}
        </div>
      </div>
    </section>
  )
}

export default MovieSection
