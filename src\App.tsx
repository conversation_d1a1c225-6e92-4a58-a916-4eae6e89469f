import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'

function App() {
  const [count, setCount] = useState(0)

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
      <div className="text-center space-y-8 p-8">
        <div className="flex justify-center space-x-8">
          <a href="https://vite.dev" target="_blank" className="hover:scale-110 transition-transform">
            <img src={viteLogo} className="h-24 w-24" alt="Vite logo" />
          </a>
          <a href="https://react.dev" target="_blank" className="hover:scale-110 transition-transform">
            <img src={reactLogo} className="h-24 w-24 animate-spin" alt="React logo" />
          </a>
        </div>

        <h1 className="text-6xl font-bold text-white mb-8">
          <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            Vite + React + TypeScript + SWC
          </span>
        </h1>

        <div className="bg-white/10 backdrop-blur-lg rounded-xl p-8 border border-white/20">
          <button
            onClick={() => setCount((count) => count + 1)}
            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg"
          >
            Count is {count}
          </button>
          <p className="text-white/80 mt-4">
            Edit <code className="bg-gray-800 px-2 py-1 rounded text-blue-300">src/App.tsx</code> and save to test HMR
          </p>
        </div>

        <div className="text-white/60 space-y-2">
          <p>✅ React 19 with TypeScript</p>
          <p>✅ Vite with SWC for fast compilation</p>
          <p>✅ TailwindCSS for styling</p>
          <p className="text-green-400 font-semibold">🎉 Setup completed successfully!</p>
        </div>

        <p className="text-white/50 text-sm">
          Click on the Vite and React logos to learn more
        </p>
      </div>
    </div>
  )
}

export default App
