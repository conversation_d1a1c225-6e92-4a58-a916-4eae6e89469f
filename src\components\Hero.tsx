import { useState, useEffect } from 'react'
import type { Movie } from '../types/movie'
import movieApi from '../services/movieApi'

const Hero = () => {
  const [featuredMovie, setFeaturedMovie] = useState<Movie | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchFeaturedMovie = async () => {
      try {
        const response = await movieApi.getTrendingMovies(1)
        if (response.status && response.data.items.length > 0) {
          setFeaturedMovie(response.data.items[0])
        }
      } catch (error) {
        console.error('Error fetching featured movie:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchFeaturedMovie()
  }, [])

  // Helper function to get image URL
  const getImageUrl = (url: string) => {
    if (!url) return 'https://images.unsplash.com/photo-1489599735734-79b4169c2a78?w=1920&h=1080&fit=crop'
    if (url.startsWith('http')) return url
    return `https://img.phimapi.com/${url}`
  }

  // Get rating from TMDB or default
  const getRating = () => {
    return featuredMovie?.tmdb?.vote_average ? featuredMovie.tmdb.vote_average.toFixed(1) : '8.5'
  }

  // Truncate content
  const truncateContent = (content: string, maxLength: number = 200) => {
    if (!content) return 'Đang cập nhật nội dung phim...'
    const cleanContent = content.replace(/<[^>]*>/g, '') // Remove HTML tags
    return cleanContent.length > maxLength
      ? cleanContent.substring(0, maxLength) + '...'
      : cleanContent
  }

  if (loading) {
    return (
      <section className="relative h-[70vh] md:h-[80vh] overflow-hidden bg-gray-800 animate-pulse">
        <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/50 to-transparent"></div>
        <div className="relative z-10 h-full flex items-center px-4 md:px-8 lg:px-16">
          <div className="max-w-2xl space-y-6">
            <div className="bg-gray-700 h-8 w-48 rounded animate-pulse"></div>
            <div className="bg-gray-700 h-16 w-full rounded animate-pulse"></div>
            <div className="bg-gray-700 h-24 w-full rounded animate-pulse"></div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="relative h-[70vh] md:h-[80vh] overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <img
          src={featuredMovie ? getImageUrl(featuredMovie.poster_url || featuredMovie.thumb_url) : 'https://images.unsplash.com/photo-1489599735734-79b4169c2a78?w=1920&h=1080&fit=crop'}
          alt={featuredMovie?.name || "Featured Movie"}
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement
            target.src = 'https://images.unsplash.com/photo-1489599735734-79b4169c2a78?w=1920&h=1080&fit=crop'
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/50 to-transparent"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-transparent to-transparent"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 h-full flex items-center px-4 md:px-8 lg:px-16">
        <div className="max-w-2xl space-y-6">
          {/* Badge */}
          <div className="flex items-center space-x-2">
            <span className="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
              🔥 Phim Hot Nhất
            </span>
          </div>

          {/* Title */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight">
            {featuredMovie?.name || "Đang tải..."}
          </h1>

          {/* Description */}
          <p className="text-lg md:text-xl text-gray-300 leading-relaxed max-w-xl">
            {featuredMovie ? truncateContent(featuredMovie.content) : "Đang tải nội dung phim..."}
          </p>

          {/* Movie Info */}
          <div className="flex items-center space-x-6 text-sm text-gray-300">
            <span className="flex items-center space-x-1">
              <svg className="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              <span>{getRating()}</span>
            </span>
            <span>{featuredMovie?.year || "2024"}</span>
            <span>{featuredMovie?.time || "N/A"}</span>
            <span className="bg-gray-700 px-2 py-1 rounded text-xs">{featuredMovie?.quality || "HD"}</span>
          </div>

          {/* Genres */}
          <div className="flex flex-wrap gap-2">
            {featuredMovie?.category.slice(0, 3).map((cat) => (
              <span key={cat.id} className="bg-gray-800/80 text-gray-300 px-3 py-1 rounded-full text-sm">
                {cat.name}
              </span>
            )) || (
              <>
                <span className="bg-gray-800/80 text-gray-300 px-3 py-1 rounded-full text-sm">Hành Động</span>
                <span className="bg-gray-800/80 text-gray-300 px-3 py-1 rounded-full text-sm">Phiêu Lưu</span>
                <span className="bg-gray-800/80 text-gray-300 px-3 py-1 rounded-full text-sm">Khoa Học Viễn Tưởng</span>
              </>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 pt-4">
            <button className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center space-x-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
              </svg>
              <span>Xem Ngay</span>
            </button>

            <button className="bg-gray-800/80 hover:bg-gray-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center space-x-2 border border-gray-600">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              <span>Danh Sách</span>
            </button>

            <button className="bg-gray-800/80 hover:bg-gray-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center space-x-2 border border-gray-600">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Chi Tiết</span>
            </button>
          </div>
        </div>
      </div>

      {/* Volume Control */}
      <div className="absolute bottom-8 right-8 z-10">
        <button className="bg-gray-800/80 hover:bg-gray-700 text-white p-3 rounded-full border border-gray-600 transition-colors">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5 7h4l5-5v20l-5-5H5a2 2 0 01-2-2V9a2 2 0 012-2z" />
          </svg>
        </button>
      </div>
    </section>
  )
}

export default Hero
