import { ApiResponse, MovieDetail, API_ENDPOINTS, MovieType, SortField, SortType } from '../types/movie'

class MovieApiService {
  private baseUrl = API_ENDPOINTS.BASE_URL

  // Helper method to build query string
  private buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams()

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString())
      }
    })

    return searchParams.toString()
  }

  // Get new movies
  async getNewMovies(page: number = 1): Promise<ApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.NEW_MOVIES}?page=${page}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      })
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()
      console.log('Raw API Response:', data) // Debug log
      return data
    } catch (error) {
      console.error('Error fetching new movies:', error)
      throw error
    }
  }

  // Get movies by type (phim-bo, phim-le, tv-shows, hoat-hinh)
  async getMoviesByType(
    type: MovieType,
    options: {
      page?: number
      sortField?: SortField
      sortType?: SortType
      category?: string
      country?: string
      year?: number
      limit?: number
    } = {}
  ): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        category: options.category,
        country: options.country,
        year: options.year,
        limit: options.limit || 20
      })

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.LIST}/${type}?${queryString}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error fetching ${type} movies:`, error)
      throw error
    }
  }

  // Get movies by category
  async getMoviesByCategory(
    category: string,
    options: {
      page?: number
      sortField?: SortField
      sortType?: SortType
      country?: string
      year?: number
      limit?: number
    } = {}
  ): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        country: options.country,
        year: options.year,
        limit: options.limit || 20
      })

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.CATEGORY}/${category}?${queryString}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error fetching movies by category ${category}:`, error)
      throw error
    }
  }

  // Get movies by country
  async getMoviesByCountry(
    country: string,
    options: {
      page?: number
      sortField?: SortField
      sortType?: SortType
      category?: string
      year?: number
      limit?: number
    } = {}
  ): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        category: options.category,
        year: options.year,
        limit: options.limit || 20
      })

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.COUNTRY}/${country}?${queryString}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error fetching movies by country ${country}:`, error)
      throw error
    }
  }

  // Get movies by year
  async getMoviesByYear(
    year: number,
    options: {
      page?: number
      sortField?: SortField
      sortType?: SortType
      category?: string
      country?: string
      limit?: number
    } = {}
  ): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        category: options.category,
        country: options.country,
        limit: options.limit || 20
      })

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.YEAR}/${year}?${queryString}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error fetching movies by year ${year}:`, error)
      throw error
    }
  }

  // Search movies
  async searchMovies(
    keyword: string,
    options: {
      page?: number
      sortField?: SortField
      sortType?: SortType
      category?: string
      country?: string
      year?: number
      limit?: number
    } = {}
  ): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        keyword,
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        category: options.category,
        country: options.country,
        year: options.year,
        limit: options.limit || 20
      })

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.SEARCH}?${queryString}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error searching movies with keyword "${keyword}":`, error)
      throw error
    }
  }

  // Get movie detail
  async getMovieDetail(slug: string): Promise<MovieDetail> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.MOVIE_DETAIL}/${slug}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error fetching movie detail for slug "${slug}":`, error)
      throw error
    }
  }

  // Get trending movies (new movies with high rating)
  async getTrendingMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getNewMovies(1)
  }

  // Get popular movies (sorted by vote_average)
  async getPopularMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByType('phim-le', {
      sortField: 'modified.time',
      sortType: 'desc',
      limit
    })
  }

  // Get action movies
  async getActionMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByCategory('hanh-dong', { limit })
  }

  // Get comedy movies
  async getComedyMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByCategory('hai-huoc', { limit })
  }

  // Get TV shows
  async getTVShows(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByType('tv-shows', { limit })
  }
}

// Export singleton instance
export const movieApi = new MovieApiService()
export default movieApi
